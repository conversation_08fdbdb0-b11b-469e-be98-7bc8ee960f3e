'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { FileUploader } from '@/components/file-uploader';
import { useS3ImageUpload } from '@/hooks/useS3Upload';
// Alternative: import { useDirectS3Upload } from '@/hooks/useS3UploadAlternative';
import { X } from 'lucide-react';

interface ImageUploadFieldProps {
  label: string;
  value: string;
  onChange: (url: string) => void;
  uploadPath?: string;
  disabled?: boolean;
}

export function ImageUploadField({
  label,
  value,
  onChange,
  uploadPath = '/uploads/cms',
  disabled = false
}: ImageUploadFieldProps) {
  const [files, setFiles] = useState<File[]>([]);

  const { upload, isUploading } = useS3ImageUpload({
    onSuccess: (data) => {
      onChange(data.url);
      setFiles([]);
    }
  });

  const handleFileUpload = async (uploadFiles: File[]) => {
    if (uploadFiles.length === 0) return;

    const file = uploadFiles[0];
    const fileName = `${Date.now()}-${file.name}`;

    try {
      await upload({
        file,
        fileName,
        path: uploadPath
      });
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  const handleRemoveImage = () => {
    onChange('');
    setFiles([]);
  };

  return (
    <div className='space-y-3'>
      <Label htmlFor={`${label.toLowerCase().replace(/\s+/g, '-')}`}>
        {label}
      </Label>

      <FileUploader
        value={files}
        onValueChange={setFiles}
        onUpload={handleFileUpload}
        maxFiles={1}
        maxSize={5 * 1024 * 1024} // 5MB
        accept={{ 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] }}
        disabled={disabled || isUploading}
      />

      {/* Image Preview */}
      {value && (
        <div className='relative w-full max-w-sm'>
          <div className='relative aspect-video w-full overflow-hidden rounded-lg border'>
            <img
              src={value}
              alt={`${label} preview`}
              className='object-cover'
              onError={() => {
                // Handle broken image - could show placeholder or error state
              }}
            />
          </div>
          <Button
            type='button'
            variant='destructive'
            size='sm'
            className='absolute -top-2 -right-2'
            onClick={handleRemoveImage}
            disabled={disabled}
          >
            <X className='h-3 w-3' />
          </Button>
        </div>
      )}
    </div>
  );
}
