'use client';

import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Save } from 'lucide-react';
import { useGetTermsAndConditions } from '@/hooks/useQuery';
import { useUpdateTermsAndConditions } from '@/hooks/useMutation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { QuillEditor } from '@/components/ui/quill-editor';

// Validation schema
const termsAndConditionsSchema = z.object({
  heading: z.string().min(1, 'Heading is required'),
  HTMLContent: z.string().min(1, 'Content is required'),
  lastUpdated: z.string().min(1, 'Last updated date is required')
});

type TermsAndConditionsFormData = z.infer<typeof termsAndConditionsSchema>;

export default function TermsAndConditionsEditPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch Terms and Conditions page data
  const { data: termsData, isLoading: termsLoading } = useGetTermsAndConditions();

  // Update mutation
  const updateMutation = useUpdateTermsAndConditions({
    onSuccess: () => {
      setIsSubmitting(false);
      toast.success('Terms and Conditions updated successfully');
      router.push('/dashboard/cms/terms-and-conditions');
    },
    onError: (error: any) => {
      setIsSubmitting(false);
      toast.error(error?.message || 'Failed to update Terms and Conditions');
    }
  });

  // Form setup
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<TermsAndConditionsFormData>({
    resolver: zodResolver(termsAndConditionsSchema),
    defaultValues: {
      heading: '',
      HTMLContent: '',
      lastUpdated: new Date().toISOString().split('T')[0]
    }
  });

  // Reset form when data loads
  useEffect(() => {
    if (termsData?.data?.page) {
      const pageData = termsData.data.page;
      reset({
        heading: pageData.heading,
        HTMLContent: pageData.HTMLContent,
        lastUpdated: pageData.lastUpdated ? 
          new Date(pageData.lastUpdated).toISOString().split('T')[0] : 
          new Date().toISOString().split('T')[0]
      });
    }
  }, [termsData, reset]);

  const handleBack = () => {
    router.push('/dashboard/cms/terms-and-conditions');
  };

  const onSubmit = async (data: TermsAndConditionsFormData) => {
    setIsSubmitting(true);
    updateMutation.mutate(data);
  };

  if (termsLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='mt-2 h-4 w-64' />
            </div>
          </div>
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <Skeleton className='h-6 w-32' />
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <Skeleton className='h-10 w-full' />
                  <Skeleton className='h-40 w-full' />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!termsData?.data?.page) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            Terms and Conditions page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>Terms and Conditions - Edit</h1>
              <p className='text-muted-foreground'>
                Edit the Terms and Conditions content and settings
              </p>
            </div>
          </div>
          <Button onClick={handleSubmit(onSubmit)} disabled={isSubmitting}>
            <Save className='mr-2 h-4 w-4' />
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
          {/* Terms and Conditions Content */}
          <Card>
            <CardHeader>
              <CardTitle>Terms and Conditions Content</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='heading'>Heading</Label>
                <Input
                  id='heading'
                  {...register('heading')}
                  placeholder='Enter terms and conditions heading'
                />
                {errors.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.heading.message}
                  </p>
                )}
              </div>
              
              <div>
                <Label htmlFor='lastUpdated'>Last Updated Date</Label>
                <Input
                  id='lastUpdated'
                  type='date'
                  {...register('lastUpdated')}
                />
                {errors.lastUpdated && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.lastUpdated.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor='HTMLContent'>Content</Label>
                <QuillEditor
                  value={watch('HTMLContent') || ''}
                  onChange={(value) => setValue('HTMLContent', value)}
                  placeholder='Enter terms and conditions content...'
                  disabled={isSubmitting}
                />
                {errors.HTMLContent && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.HTMLContent.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </PageContainer>
  );
}
