'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Edit } from 'lucide-react';
import { useGetPrivacyPolicy } from '@/hooks/useQuery';

export default function PrivacyPolicyViewPage() {
  const router = useRouter();

  // Fetch Privacy Policy page data
  const { data: privacyPolicyData, isLoading: privacyPolicyLoading } = useGetPrivacyPolicy();

  const handleBack = () => {
    router.push('/dashboard/cms/privacy-policy');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/privacy-policy/edit');
  };

  if (privacyPolicyLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='h-4 w-64 mt-2' />
            </div>
          </div>
          <div className='grid grid-cols-1 gap-6'>
            <Card>
              <CardHeader>
                <Skeleton className='h-6 w-32' />
              </CardHeader>
              <CardContent>
                <Skeleton className='h-40 w-full' />
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    );
  }

  const pageData = privacyPolicyData?.data?.page;

  if (!pageData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            Privacy Policy page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>Privacy Policy - View</h1>
              <p className='text-muted-foreground'>
                View the complete Privacy Policy content
              </p>
            </div>
          </div>
          <Button onClick={handleEdit}>
            <Edit className='mr-2 h-4 w-4' />
            Edit Privacy Policy
          </Button>
        </div>

        {/* Privacy Policy Content */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>{pageData.heading}</CardTitle>
              <Badge variant='primary'>privacy-policy</Badge>
            </div>
            {pageData.lastUpdated && (
              <p className='text-sm text-muted-foreground'>
                Last updated: {new Date(pageData.lastUpdated).toLocaleDateString()}
              </p>
            )}
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='prose prose-sm max-w-none'>
              <div 
                dangerouslySetInnerHTML={{ __html: pageData.HTMLContent || '<p>No content available</p>' }}
                className='text-muted-foreground leading-relaxed'
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
