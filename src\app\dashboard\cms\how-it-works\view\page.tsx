'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Edit } from 'lucide-react';
import { useGetHowItWorksPage } from '@/hooks/useQuery';
import Image from 'next/image';
import { DEFAULT_IMAGE } from '@/constants/app.const';

export default function HowItWorksViewPage() {
  const router = useRouter();

  // Fetch How It Works page data
  const { data: howItWorksData, isLoading: howItWorksLoading } = useGetHowItWorksPage();

  const handleBack = () => {
    router.push('/dashboard/cms/how-it-works');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/how-it-works/edit');
  };

  if (howItWorksLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='h-4 w-64 mt-2' />
            </div>
          </div>
          <div className='space-y-6'>
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className='h-6 w-32' />
                </CardHeader>
                <CardContent>
                  <Skeleton className='h-20 w-full' />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </PageContainer>
    );
  }

  const pageData = howItWorksData?.data?.page;

  if (!pageData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            How It Works page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>How It Works Page - View</h1>
              <p className='text-muted-foreground'>
                View all sections of the How It Works page
              </p>
            </div>
          </div>
          <Button onClick={handleEdit}>
            <Edit className='mr-2 h-4 w-4' />
            Edit Page
          </Button>
        </div>

        {/* Main Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>Main Section</CardTitle>
              <Badge variant='primary'>main-section</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>{pageData.heading}</h3>
              <p className='text-muted-foreground leading-relaxed mb-4'>
                {pageData.description}
              </p>
              {pageData.image && (
                <div className='relative w-full h-64 rounded-lg overflow-hidden'>
                  <Image
                    src={pageData.image || DEFAULT_IMAGE}
                    alt='How It Works'
                    fill
                    className='object-cover'
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* For Candidates Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>For Candidates</CardTitle>
              <Badge variant='secondary'>for-candidates</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>
                {pageData.forCandidates.heading}
              </h3>
              <p className='text-muted-foreground leading-relaxed mb-4'>
                {pageData.forCandidates.description}
              </p>
              
              {pageData.forCandidates.steps && pageData.forCandidates.steps.length > 0 && (
                <div className='space-y-3'>
                  <h4 className='font-medium'>Steps:</h4>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {pageData.forCandidates.steps.map((step, index) => (
                      <Card key={step._id || index} className='border-l-4 border-l-green-500'>
                        <CardContent className='p-4'>
                          <div className='flex items-start gap-3'>
                            <div className='bg-green-100 text-green-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold flex-shrink-0'>
                              {index + 1}
                            </div>
                            <div className='flex-1'>
                              <h5 className='font-semibold mb-1'>{step.heading}</h5>
                              <p className='text-sm text-muted-foreground'>
                                {step.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* For Recruiters Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>For Recruiters</CardTitle>
              <Badge variant='outline'>for-recruiters</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>
                {pageData.forRecruiters.heading}
              </h3>
              <p className='text-muted-foreground leading-relaxed mb-4'>
                {pageData.forRecruiters.description}
              </p>
              
              {pageData.forRecruiters.steps && pageData.forRecruiters.steps.length > 0 && (
                <div className='space-y-3'>
                  <h4 className='font-medium'>Steps:</h4>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    {pageData.forRecruiters.steps.map((step, index) => (
                      <Card key={step._id || index} className='border-l-4 border-l-purple-500'>
                        <CardContent className='p-4'>
                          <div className='flex items-start gap-3'>
                            <div className='bg-purple-100 text-purple-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-semibold flex-shrink-0'>
                              {index + 1}
                            </div>
                            <div className='flex-1'>
                              <h5 className='font-semibold mb-1'>{step.heading}</h5>
                              <p className='text-sm text-muted-foreground'>
                                {step.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Benefits Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>Benefits</CardTitle>
              <Badge variant='secondary'>benefits</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            {pageData.benefits && pageData.benefits.length > 0 && (
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {pageData.benefits.map((benefit, index) => (
                  <Card key={benefit._id || index} className='border-l-4 border-l-orange-500'>
                    <CardContent className='p-4'>
                      <div className='flex items-start gap-3'>
                        {benefit.iconImage && (
                          <div className='relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0'>
                            <Image
                              src={benefit.iconImage || DEFAULT_IMAGE}
                              alt={benefit.heading}
                              fill
                              className='object-cover'
                            />
                          </div>
                        )}
                        <div className='flex-1'>
                          <h4 className='font-semibold mb-1'>{benefit.heading}</h4>
                          <p className='text-sm text-muted-foreground'>
                            {benefit.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
