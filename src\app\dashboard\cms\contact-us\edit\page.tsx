'use client';

import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Save } from 'lucide-react';
import { useGetContactUsPage } from '@/hooks/useQuery';
import { useUpdateContactUsSection } from '@/hooks/useMutation';
import { useEffect, useState } from 'react';
import { ImageUploadField } from '@/components/cms/image-upload-field';

// Validation schema
const contactUsSchema = z.object({
  heading: z.string().min(1, 'Heading is required'),
  description: z.string().min(1, 'Description is required'),
  image: z.string().min(1, 'Image is required')
});

type ContactUsFormData = z.infer<typeof contactUsSchema>;

export default function ContactUsEditPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch Contact Us page data
  const { data: contactUsData, isLoading: contactUsLoading } =
    useGetContactUsPage();

  // Update mutation
  const updateMutation = useUpdateContactUsSection({
    onSuccess: () => {
      setIsSubmitting(false);
      router.push('/dashboard/cms/contact-us');
    },
    onError: () => {
      setIsSubmitting(false);
    }
  });

  // Form setup
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<ContactUsFormData>({
    resolver: zodResolver(contactUsSchema),
    defaultValues: {
      heading: '',
      description: '',
      image: ''
    }
  });

  // Reset form when data loads
  useEffect(() => {
    if (contactUsData?.data?.page) {
      const pageData = contactUsData.data.page;
      reset({
        heading: pageData.heading,
        description: pageData.description,
        image: pageData.image
      });
    }
  }, [contactUsData, reset]);

  const handleBack = () => {
    router.push('/dashboard/cms/contact-us');
  };

  const onSubmit = async (data: ContactUsFormData) => {
    setIsSubmitting(true);
    updateMutation.mutate(data);
  };

  if (contactUsLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='mt-2 h-4 w-64' />
            </div>
          </div>
          <Card>
            <CardHeader>
              <Skeleton className='h-6 w-32' />
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <Skeleton className='h-10 w-full' />
                <Skeleton className='h-20 w-full' />
                <Skeleton className='h-10 w-full' />
              </div>
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    );
  }

  if (!contactUsData?.data?.page) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            Contact Us page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>Contact Us Page - Edit</h1>
              <p className='text-muted-foreground'>
                Edit the Contact Us page content
              </p>
            </div>
          </div>
          <Button onClick={handleSubmit(onSubmit)} disabled={isSubmitting}>
            <Save className='mr-2 h-4 w-4' />
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
          {/* Main Section */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Us Section</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='heading'>Heading</Label>
                <Input
                  id='heading'
                  {...register('heading')}
                  placeholder='Enter contact us heading'
                />
                {errors.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.heading.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='description'>Description</Label>
                <Textarea
                  id='description'
                  {...register('description')}
                  placeholder='Enter contact us description'
                  rows={6}
                />
                {errors.description && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.description.message}
                  </p>
                )}
              </div>
              <ImageUploadField
                label='Contact Us Image'
                value={watch('image') || ''}
                onChange={(url) => setValue('image', url)}
                uploadPath='/uploads/cms/contact-us'
                disabled={isSubmitting}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className='flex justify-end gap-4'>
            <Button
              type='button'
              variant='outline'
              onClick={handleBack}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={isSubmitting}>
              <Save className='mr-2 h-4 w-4' />
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    </PageContainer>
  );
}
