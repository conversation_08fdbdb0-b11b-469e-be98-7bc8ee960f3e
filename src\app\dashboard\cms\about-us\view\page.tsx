'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Edit, Image as ImageIcon } from 'lucide-react';
import { useGetAboutUsPage } from '@/hooks/useQuery';
import Image from 'next/image';
import { DEFAULT_IMAGE } from '@/constants/app.const';

export default function AboutUsViewPage() {
  const router = useRouter();

  // Fetch About Us page data
  const { data: aboutUsData, isLoading: aboutUsLoading } = useGetAboutUsPage();

  const handleBack = () => {
    router.push('/dashboard/cms/about-us');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/about-us/edit');
  };

  if (aboutUsLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='h-4 w-64 mt-2' />
            </div>
          </div>
          <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className='h-6 w-32' />
                </CardHeader>
                <CardContent>
                  <Skeleton className='h-20 w-full' />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </PageContainer>
    );
  }

  const pageData = aboutUsData?.data?.page;

  if (!pageData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            About Us page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>About Us Page - View</h1>
              <p className='text-muted-foreground'>
                View all sections of the About Us page
              </p>
            </div>
          </div>
          <Button onClick={handleEdit}>
            <Edit className='mr-2 h-4 w-4' />
            Edit Page
          </Button>
        </div>

        {/* Our Mission Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>Our Mission</CardTitle>
              <Badge variant='primary'>our-mission</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>
                {pageData.ourMission.heading}
              </h3>
              <p className='text-muted-foreground leading-relaxed'>
                {pageData.ourMission.description}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* About Us Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>About Us</CardTitle>
              <Badge variant='secondary'>about-us</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>
                {pageData.aboutUs.heading}
              </h3>
              <p className='text-muted-foreground leading-relaxed mb-4'>
                {pageData.aboutUs.description}
              </p>
              {pageData.aboutUs.image && (
                <div className='relative w-full h-64 rounded-lg overflow-hidden'>
                  <Image
                    src={pageData.aboutUs.image || DEFAULT_IMAGE}
                    alt='About Us'
                    fill
                    className='object-cover'
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Our Vision Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>Our Vision</CardTitle>
              <Badge variant='outline'>our-vision</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>
                {pageData.ourVision.heading}
              </h3>
              <p className='text-muted-foreground leading-relaxed'>
                {pageData.ourVision.description}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Our Values Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>Our Values</CardTitle>
              <Badge variant='secondary'>our-values</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>
                {pageData.ourValues.heading}
              </h3>
              <p className='text-muted-foreground leading-relaxed mb-4'>
                {pageData.ourValues.description}
              </p>
              
              {pageData.ourValues.values && pageData.ourValues.values.length > 0 && (
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  {pageData.ourValues.values.map((value, index) => (
                    <Card key={value._id || index} className='border-l-4 border-l-primary'>
                      <CardContent className='p-4'>
                        <div className='flex items-start gap-3'>
                          {value.iconImage && (
                            <div className='relative w-12 h-12 rounded-lg overflow-hidden flex-shrink-0'>
                              <Image
                                src={value.iconImage || DEFAULT_IMAGE}
                                alt={value.name}
                                fill
                                className='object-cover'
                              />
                            </div>
                          )}
                          <div className='flex-1'>
                            <h4 className='font-semibold mb-1'>{value.name}</h4>
                            <p className='text-sm text-muted-foreground'>
                              {value.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
