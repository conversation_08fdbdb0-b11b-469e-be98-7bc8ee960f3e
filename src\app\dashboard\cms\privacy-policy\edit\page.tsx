'use client';

import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Save } from 'lucide-react';
import { useGetPrivacyPolicy } from '@/hooks/useQuery';
import { useUpdatePrivacyPolicy } from '@/hooks/useMutation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { QuillEditor } from '@/components/ui/quill-editor';

// Validation schema
const privacyPolicySchema = z.object({
  heading: z.string().min(1, 'Heading is required'),
  HTMLContent: z.string().min(1, 'Content is required'),
  lastUpdated: z.string().min(1, 'Last updated date is required')
});

type PrivacyPolicyFormData = z.infer<typeof privacyPolicySchema>;

export default function PrivacyPolicyEditPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch Privacy Policy page data
  const { data: privacyPolicyData, isLoading: privacyPolicyLoading } =
    useGetPrivacyPolicy();

  // Update mutation
  const updateMutation = useUpdatePrivacyPolicy({
    onSuccess: () => {
      setIsSubmitting(false);
      toast.success('Privacy Policy updated successfully');
      router.push('/dashboard/cms/privacy-policy');
    },
    onError: (error: any) => {
      setIsSubmitting(false);
      toast.error(error?.message || 'Failed to update Privacy Policy');
    }
  });

  // Form setup
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<PrivacyPolicyFormData>({
    resolver: zodResolver(privacyPolicySchema),
    defaultValues: {
      heading: '',
      HTMLContent: '',
      lastUpdated: new Date().toISOString().split('T')[0]
    }
  });

  // Reset form when data loads
  useEffect(() => {
    if (privacyPolicyData?.data?.page) {
      const pageData = privacyPolicyData.data.page;
      reset({
        heading: pageData.heading,
        HTMLContent: pageData.HTMLContent,
        lastUpdated: pageData.lastUpdated
          ? new Date(pageData.lastUpdated).toISOString().split('T')[0]
          : new Date().toISOString().split('T')[0]
      });
    }
  }, [privacyPolicyData, reset]);

  const handleBack = () => {
    router.push('/dashboard/cms/privacy-policy');
  };

  const onSubmit = async (data: PrivacyPolicyFormData) => {
    setIsSubmitting(true);
    updateMutation.mutate(data);
  };

  if (privacyPolicyLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='mt-2 h-4 w-64' />
            </div>
          </div>
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <Skeleton className='h-6 w-32' />
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <Skeleton className='h-10 w-full' />
                  <Skeleton className='h-40 w-full' />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageContainer>
    );
  }

  // if (!privacyPolicyData?.data?.page) {
  //   return (
  //     <PageContainer>
  //       <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
  //         <h2 className='text-2xl font-bold'>No Data Available</h2>
  //         <p className='text-muted-foreground'>
  //           Privacy Policy page data could not be loaded.
  //         </p>
  //         <Button onClick={handleBack}>
  //           <ArrowLeft className='mr-2 h-4 w-4' />
  //           Go Back
  //         </Button>
  //       </div>
  //     </PageContainer>
  //   );
  // }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>Privacy Policy - Edit</h1>
              <p className='text-muted-foreground'>
                Edit the Privacy Policy content and settings
              </p>
            </div>
          </div>
          <Button onClick={handleSubmit(onSubmit)} disabled={isSubmitting}>
            <Save className='mr-2 h-4 w-4' />
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
          {/* Privacy Policy Content */}
          <Card>
            <CardHeader>
              <CardTitle>Privacy Policy Content</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='heading'>Heading</Label>
                <Input
                  id='heading'
                  {...register('heading')}
                  placeholder='Enter privacy policy heading'
                />
                {errors.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.heading.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor='lastUpdated'>Last Updated Date</Label>
                <Input
                  id='lastUpdated'
                  type='date'
                  {...register('lastUpdated')}
                />
                {errors.lastUpdated && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.lastUpdated.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor='HTMLContent'>Content</Label>
                <QuillEditor
                  value={watch('HTMLContent') || ''}
                  onChange={(value) => setValue('HTMLContent', value)}
                  placeholder='Enter privacy policy content...'
                  disabled={isSubmitting}
                />
                {errors.HTMLContent && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.HTMLContent.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </PageContainer>
  );
}
