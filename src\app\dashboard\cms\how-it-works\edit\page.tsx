'use client';

import { useRouter } from 'next/navigation';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Save, Plus, Trash2 } from 'lucide-react';
import { useGetHowItWorksPage } from '@/hooks/useQuery';
import { useUpdateHowItWorksSection } from '@/hooks/useMutation';
import { useEffect, useState } from 'react';
import { ImageUploadField } from '@/components/cms/image-upload-field';

// Validation schema
const howItWorksSchema = z.object({
  heading: z.string().min(1, 'Heading is required'),
  description: z.string().min(1, 'Description is required'),
  image: z.string().min(1, 'Image is required'),
  benefits: z.array(
    z.object({
      heading: z.string().min(1, 'Heading is required'),
      description: z.string().min(1, 'Description is required'),
      iconImage: z.string().min(1, 'Icon image is required')
    })
  ),
  forCandidates: z.object({
    heading: z.string().min(1, 'Heading is required'),
    description: z.string().min(1, 'Description is required'),
    steps: z.array(
      z.object({
        heading: z.string().min(1, 'Heading is required'),
        description: z.string().min(1, 'Description is required')
      })
    )
  }),
  forRecruiters: z.object({
    heading: z.string().min(1, 'Heading is required'),
    description: z.string().min(1, 'Description is required'),
    steps: z.array(
      z.object({
        heading: z.string().min(1, 'Heading is required'),
        description: z.string().min(1, 'Description is required')
      })
    )
  })
});

type HowItWorksFormData = z.infer<typeof howItWorksSchema>;

export default function HowItWorksEditPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch How It Works page data
  const { data: howItWorksData, isLoading: howItWorksLoading } =
    useGetHowItWorksPage();

  // Update mutation
  const updateMutation = useUpdateHowItWorksSection({
    onSuccess: () => {
      setIsSubmitting(false);
      router.push('/dashboard/cms/how-it-works');
    },
    onError: () => {
      setIsSubmitting(false);
    }
  });

  // Form setup
  const {
    register,
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<HowItWorksFormData>({
    resolver: zodResolver(howItWorksSchema),
    defaultValues: {
      heading: '',
      description: '',
      image: '',
      benefits: [],
      forCandidates: { heading: '', description: '', steps: [] },
      forRecruiters: { heading: '', description: '', steps: [] }
    }
  });

  // Field arrays
  const {
    fields: benefitFields,
    append: appendBenefit,
    remove: removeBenefit
  } = useFieldArray({
    control,
    name: 'benefits'
  });

  const {
    fields: candidateStepFields,
    append: appendCandidateStep,
    remove: removeCandidateStep
  } = useFieldArray({
    control,
    name: 'forCandidates.steps'
  });

  const {
    fields: recruiterStepFields,
    append: appendRecruiterStep,
    remove: removeRecruiterStep
  } = useFieldArray({
    control,
    name: 'forRecruiters.steps'
  });

  // Reset form when data loads
  useEffect(() => {
    if (howItWorksData?.data?.page) {
      const pageData = howItWorksData.data.page;
      reset({
        heading: pageData.heading,
        description: pageData.description,
        image: pageData.image,
        benefits: pageData.benefits.map((benefit) => ({
          heading: benefit.heading,
          description: benefit.description,
          iconImage: benefit.iconImage
        })),
        forCandidates: {
          heading: pageData.forCandidates.heading,
          description: pageData.forCandidates.description,
          steps: pageData.forCandidates.steps.map((step) => ({
            heading: step.heading,
            description: step.description
          }))
        },
        forRecruiters: {
          heading: pageData.forRecruiters.heading,
          description: pageData.forRecruiters.description,
          steps: pageData.forRecruiters.steps.map((step) => ({
            heading: step.heading,
            description: step.description
          }))
        }
      });
    }
  }, [howItWorksData, reset]);

  const handleBack = () => {
    router.push('/dashboard/cms/how-it-works');
  };

  const onSubmit = async (data: HowItWorksFormData) => {
    setIsSubmitting(true);
    updateMutation.mutate(data);
  };

  const addBenefit = () => {
    appendBenefit({
      heading: '',
      description: '',
      iconImage: ''
    });
  };

  const addCandidateStep = () => {
    appendCandidateStep({
      heading: '',
      description: ''
    });
  };

  const addRecruiterStep = () => {
    appendRecruiterStep({
      heading: '',
      description: ''
    });
  };

  if (howItWorksLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='mt-2 h-4 w-64' />
            </div>
          </div>
          <div className='space-y-6'>
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className='h-6 w-32' />
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <Skeleton className='h-10 w-full' />
                    <Skeleton className='h-20 w-full' />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </PageContainer>
    );
  }

  if (!howItWorksData?.data?.page) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            How It Works page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>How It Works Page - Edit</h1>
              <p className='text-muted-foreground'>
                Edit all sections of the How It Works page
              </p>
            </div>
          </div>
          <Button onClick={handleSubmit(onSubmit)} disabled={isSubmitting}>
            <Save className='mr-2 h-4 w-4' />
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
          {/* Main Section */}
          <Card>
            <CardHeader>
              <CardTitle>Main Section</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='heading'>Heading</Label>
                <Input
                  id='heading'
                  {...register('heading')}
                  placeholder='Enter main heading'
                />
                {errors.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.heading.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='description'>Description</Label>
                <Textarea
                  id='description'
                  {...register('description')}
                  placeholder='Enter main description'
                  rows={4}
                />
                {errors.description && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.description.message}
                  </p>
                )}
              </div>
              <ImageUploadField
                label='Main Section Image'
                value={watch('image') || ''}
                onChange={(url) => setValue('image', url)}
                uploadPath='/uploads/cms/how-it-works'
                disabled={isSubmitting}
              />
            </CardContent>
          </Card>

          {/* For Candidates Section */}
          <Card>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <CardTitle>For Candidates</CardTitle>
                <Button type='button' onClick={addCandidateStep} size='sm'>
                  <Plus className='mr-2 h-4 w-4' />
                  Add Step
                </Button>
              </div>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='forCandidates.heading'>Heading</Label>
                <Input
                  id='forCandidates.heading'
                  {...register('forCandidates.heading')}
                  placeholder='Enter candidates section heading'
                />
                {errors.forCandidates?.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.forCandidates.heading.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='forCandidates.description'>Description</Label>
                <Textarea
                  id='forCandidates.description'
                  {...register('forCandidates.description')}
                  placeholder='Enter candidates section description'
                  rows={4}
                />
                {errors.forCandidates?.description && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.forCandidates.description.message}
                  </p>
                )}
              </div>

              {/* Candidate Steps */}
              <div className='space-y-4'>
                <Label>Steps</Label>
                {candidateStepFields.map((field, index) => (
                  <Card
                    key={field.id}
                    className='border-l-4 border-l-green-500'
                  >
                    <CardHeader className='pb-3'>
                      <div className='flex items-center justify-between'>
                        <h4 className='text-sm font-medium'>
                          Step {index + 1}
                        </h4>
                        <Button
                          type='button'
                          variant='outline'
                          size='sm'
                          onClick={() => removeCandidateStep(index)}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className='space-y-3'>
                      <div>
                        <Label htmlFor={`forCandidates.steps.${index}.heading`}>
                          Heading
                        </Label>
                        <Input
                          id={`forCandidates.steps.${index}.heading`}
                          {...register(`forCandidates.steps.${index}.heading`)}
                          placeholder='Enter step heading'
                        />
                        {errors.forCandidates?.steps?.[index]?.heading && (
                          <p className='mt-1 text-sm text-red-500'>
                            {
                              errors.forCandidates.steps[index]?.heading
                                ?.message
                            }
                          </p>
                        )}
                      </div>
                      <div>
                        <Label
                          htmlFor={`forCandidates.steps.${index}.description`}
                        >
                          Description
                        </Label>
                        <Textarea
                          id={`forCandidates.steps.${index}.description`}
                          {...register(
                            `forCandidates.steps.${index}.description`
                          )}
                          placeholder='Enter step description'
                          rows={3}
                        />
                        {errors.forCandidates?.steps?.[index]?.description && (
                          <p className='mt-1 text-sm text-red-500'>
                            {
                              errors.forCandidates.steps[index]?.description
                                ?.message
                            }
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
                {candidateStepFields.length === 0 && (
                  <div className='text-muted-foreground py-8 text-center'>
                    <p>No steps added yet. Click "Add Step" to get started.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* For Recruiters Section */}
          <Card>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <CardTitle>For Recruiters</CardTitle>
                <Button type='button' onClick={addRecruiterStep} size='sm'>
                  <Plus className='mr-2 h-4 w-4' />
                  Add Step
                </Button>
              </div>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label htmlFor='forRecruiters.heading'>Heading</Label>
                <Input
                  id='forRecruiters.heading'
                  {...register('forRecruiters.heading')}
                  placeholder='Enter recruiters section heading'
                />
                {errors.forRecruiters?.heading && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.forRecruiters.heading.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor='forRecruiters.description'>Description</Label>
                <Textarea
                  id='forRecruiters.description'
                  {...register('forRecruiters.description')}
                  placeholder='Enter recruiters section description'
                  rows={4}
                />
                {errors.forRecruiters?.description && (
                  <p className='mt-1 text-sm text-red-500'>
                    {errors.forRecruiters.description.message}
                  </p>
                )}
              </div>

              {/* Recruiter Steps */}
              <div className='space-y-4'>
                <Label>Steps</Label>
                {recruiterStepFields.map((field, index) => (
                  <Card
                    key={field.id}
                    className='border-l-4 border-l-purple-500'
                  >
                    <CardHeader className='pb-3'>
                      <div className='flex items-center justify-between'>
                        <h4 className='text-sm font-medium'>
                          Step {index + 1}
                        </h4>
                        <Button
                          type='button'
                          variant='outline'
                          size='sm'
                          onClick={() => removeRecruiterStep(index)}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className='space-y-3'>
                      <div>
                        <Label htmlFor={`forRecruiters.steps.${index}.heading`}>
                          Heading
                        </Label>
                        <Input
                          id={`forRecruiters.steps.${index}.heading`}
                          {...register(`forRecruiters.steps.${index}.heading`)}
                          placeholder='Enter step heading'
                        />
                        {errors.forRecruiters?.steps?.[index]?.heading && (
                          <p className='mt-1 text-sm text-red-500'>
                            {
                              errors.forRecruiters.steps[index]?.heading
                                ?.message
                            }
                          </p>
                        )}
                      </div>
                      <div>
                        <Label
                          htmlFor={`forRecruiters.steps.${index}.description`}
                        >
                          Description
                        </Label>
                        <Textarea
                          id={`forRecruiters.steps.${index}.description`}
                          {...register(
                            `forRecruiters.steps.${index}.description`
                          )}
                          placeholder='Enter step description'
                          rows={3}
                        />
                        {errors.forRecruiters?.steps?.[index]?.description && (
                          <p className='mt-1 text-sm text-red-500'>
                            {
                              errors.forRecruiters.steps[index]?.description
                                ?.message
                            }
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
                {recruiterStepFields.length === 0 && (
                  <div className='text-muted-foreground py-8 text-center'>
                    <p>No steps added yet. Click "Add Step" to get started.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Benefits Section */}
          <Card>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <CardTitle>Benefits</CardTitle>
                <Button type='button' onClick={addBenefit} size='sm'>
                  <Plus className='mr-2 h-4 w-4' />
                  Add Benefit
                </Button>
              </div>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-4'>
                <Label>Benefits</Label>
                {benefitFields.map((field, index) => (
                  <Card
                    key={field.id}
                    className='border-l-4 border-l-orange-500'
                  >
                    <CardHeader className='pb-3'>
                      <div className='flex items-center justify-between'>
                        <h4 className='text-sm font-medium'>
                          Benefit {index + 1}
                        </h4>
                        <Button
                          type='button'
                          variant='outline'
                          size='sm'
                          onClick={() => removeBenefit(index)}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className='space-y-3'>
                      <div>
                        <Label htmlFor={`benefits.${index}.heading`}>
                          Heading
                        </Label>
                        <Input
                          id={`benefits.${index}.heading`}
                          {...register(`benefits.${index}.heading`)}
                          placeholder='Enter benefit heading'
                        />
                        {errors.benefits?.[index]?.heading && (
                          <p className='mt-1 text-sm text-red-500'>
                            {errors.benefits[index]?.heading?.message}
                          </p>
                        )}
                      </div>
                      <div>
                        <Label htmlFor={`benefits.${index}.description`}>
                          Description
                        </Label>
                        <Textarea
                          id={`benefits.${index}.description`}
                          {...register(`benefits.${index}.description`)}
                          placeholder='Enter benefit description'
                          rows={3}
                        />
                        {errors.benefits?.[index]?.description && (
                          <p className='mt-1 text-sm text-red-500'>
                            {errors.benefits[index]?.description?.message}
                          </p>
                        )}
                      </div>
                      <ImageUploadField
                        label='Icon Image'
                        value={watch(`benefits.${index}.iconImage`) || ''}
                        onChange={(url) =>
                          setValue(`benefits.${index}.iconImage`, url)
                        }
                        uploadPath='/uploads/cms/how-it-works/icons'
                        disabled={isSubmitting}
                      />
                    </CardContent>
                  </Card>
                ))}
                {benefitFields.length === 0 && (
                  <div className='text-muted-foreground py-8 text-center'>
                    <p>
                      No benefits added yet. Click "Add Benefit" to get started.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </PageContainer>
  );
}
