'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Eye, Edit } from 'lucide-react';
import { useGetTermsAndConditions } from '@/hooks/useQuery';

export default function TermsAndConditionsCMSPage() {
  const router = useRouter();

  // Fetch Terms and Conditions page data
  const { data: termsData, isLoading: termsLoading } = useGetTermsAndConditions();

  const handleView = () => {
    router.push('/dashboard/cms/terms-and-conditions/view');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/terms-and-conditions/edit');
  };

  const renderContentCard = () => {
    if (termsLoading) {
      return (
        <Card className='h-full'>
          <CardHeader>
            <Skeleton className='h-6 w-3/4' />
            <Skeleton className='h-4 w-full' />
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-2/3' />
            </div>
          </CardContent>
        </Card>
      );
    }

    const pageData = termsData?.data?.page;

    return (
      <Card className='h-full transition-all hover:shadow-md bg-green-50 border-green-200'>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-lg'>Terms and Conditions Content</CardTitle>
            <Badge variant='secondary'>terms-and-conditions</Badge>
          </div>
          <p className='text-muted-foreground text-sm'>
            Manage terms and conditions content and settings
          </p>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {pageData ? (
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium'>Heading:</span>
                  <p className='text-muted-foreground truncate text-sm'>
                    {pageData.heading}
                  </p>
                </div>
                <div>
                  <span className='text-sm font-medium'>Content:</span>
                  <p className='text-muted-foreground line-clamp-2 text-sm'>
                    {pageData.HTMLContent ? 'Rich content available' : 'No content'}
                  </p>
                </div>
                <div>
                  <span className='text-sm font-medium'>Last Updated:</span>
                  <p className='text-muted-foreground text-sm'>
                    {pageData.lastUpdated ? new Date(pageData.lastUpdated).toLocaleDateString() : 'Not set'}
                  </p>
                </div>
              </div>
            ) : (
              <p className='text-muted-foreground text-sm'>No data available</p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        <div>
          <h1 className='text-3xl font-bold'>Terms and Conditions Page</h1>
          <p className='text-muted-foreground'>
            Manage the Terms and Conditions page content. View and edit the terms
            to keep your users informed about service usage policies.
          </p>
        </div>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-1 max-w-2xl'>
          {renderContentCard()}
        </div>

        <div className='flex gap-4 pt-6'>
          <Button
            variant='outline'
            size='lg'
            onClick={handleView}
            disabled={termsLoading || !termsData?.data?.page}
          >
            <Eye className='mr-2 h-4 w-4' />
            View Terms and Conditions
          </Button>
          <Button
            size='lg'
            onClick={handleEdit}
            disabled={termsLoading || !termsData?.data?.page}
          >
            <Edit className='mr-2 h-4 w-4' />
            Edit Terms and Conditions
          </Button>
        </div>
      </div>
    </PageContainer>
  );
}
