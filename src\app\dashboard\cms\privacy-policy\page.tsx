'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Eye, Edit } from 'lucide-react';
import { useGetPrivacyPolicy } from '@/hooks/useQuery';

export default function PrivacyPolicyCMSPage() {
  const router = useRouter();

  // Fetch Privacy Policy page data
  const { data: privacyPolicyData, isLoading: privacyPolicyLoading } =
    useGetPrivacyPolicy();

  const handleView = () => {
    router.push('/dashboard/cms/privacy-policy/view');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/privacy-policy/edit');
  };

  const renderContentCard = () => {
    if (privacyPolicyLoading) {
      return (
        <Card className='h-full'>
          <CardHeader>
            <Skeleton className='h-6 w-3/4' />
            <Skeleton className='h-4 w-full' />
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-full' />
              <Skeleton className='h-4 w-2/3' />
            </div>
          </CardContent>
        </Card>
      );
    }

    const pageData = privacyPolicyData?.data?.page;

    return (
      <Card className='h-full border-blue-200 bg-blue-50 transition-all hover:shadow-md'>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-lg'>Privacy Policy Content</CardTitle>
            <Badge variant='default'>privacy-policy</Badge>
          </div>
          <p className='text-muted-foreground text-sm'>
            Manage privacy policy content and settings
          </p>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            {pageData ? (
              <div className='space-y-2'>
                <div>
                  <span className='text-sm font-medium'>Heading:</span>
                  <p className='text-muted-foreground truncate text-sm'>
                    {pageData.heading}
                  </p>
                </div>
                <div>
                  <span className='text-sm font-medium'>Content:</span>
                  <p className='text-muted-foreground line-clamp-2 text-sm'>
                    {pageData.HTMLContent
                      ? 'Rich content available'
                      : 'No content'}
                  </p>
                </div>
                <div>
                  <span className='text-sm font-medium'>Last Updated:</span>
                  <p className='text-muted-foreground text-sm'>
                    {pageData.lastUpdated
                      ? new Date(pageData.lastUpdated).toLocaleDateString()
                      : 'Not set'}
                  </p>
                </div>
              </div>
            ) : (
              <p className='text-muted-foreground text-sm'>No data available</p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        <div>
          <h1 className='text-3xl font-bold'>Privacy Policy Page</h1>
          <p className='text-muted-foreground'>
            Manage the Privacy Policy page content. View and edit the privacy
            policy to keep your users informed about data handling practices.
          </p>
        </div>

        <div className='grid max-w-2xl grid-cols-1 gap-6 md:grid-cols-1'>
          {renderContentCard()}
        </div>

        <div className='flex gap-4 pt-6'>
          <Button
            variant='outline'
            size='lg'
            onClick={handleView}
            disabled={privacyPolicyLoading}
          >
            <Eye className='mr-2 h-4 w-4' />
            View Privacy Policy
          </Button>
          <Button
            size='lg'
            onClick={handleEdit}
            disabled={privacyPolicyLoading}
          >
            <Edit className='mr-2 h-4 w-4' />
            Edit Privacy Policy
          </Button>
        </div>
      </div>
    </PageContainer>
  );
}
