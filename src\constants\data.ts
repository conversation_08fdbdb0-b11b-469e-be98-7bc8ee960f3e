import { NavItem } from '@/types';

export type Product = {
  photo_url: string;
  name: string;
  description: string;
  created_at: string;
  price: number;
  id: number;
  category: string;
  updated_at: string;
};

//Info: The following data is used for the sidebar navigation and Cmd K bar.
export const navItems: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard/overview',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: [] // Empty array as there are no child items for Dashboard
  },
  // {
  //   title: 'Product',
  //   url: '/dashboard/product',
  //   icon: 'product',
  //   shortcut: ['p', 'p'],
  //   isActive: false,
  //   items: [] // No child items
  // },
  {
    title: 'All Recruiters',
    url: '/dashboard/all-recruiters',
    icon: 'user',
    shortcut: ['r', 'r'],
    isActive: false,
    items: [] // No child items
  },
  {
    title: 'All Jobseekers',
    url: '/dashboard/all-jobseekers',
    icon: 'user',
    shortcut: ['j', 's'],
    isActive: false,
    items: [] // No child items
  },
  {
    title: 'All Jobs',
    url: '/dashboard/all-jobs',
    icon: 'product',
    shortcut: ['j', 'j'],
    isActive: false,
    items: [] // No child items
  },
  {
    title: 'Admin Settings',
    url: '/dashboard/admin-settings',
    icon: 'settings',
    shortcut: ['A', 'S'],
    isActive: false,
    items: [] // No child items
  },
  {
    title: 'CMS',
    url: '#',
    icon: 'settings',
    shortcut: ['C', 'M'],
    isActive: false,
    items: [
      {
        title: 'Homepage Sections',
        url: '/dashboard/cms/home',
        shortcut: ['H', 'S']
      },
      {
        title: 'About Us',
        url: '/dashboard/cms/about-us',
        shortcut: ['A', 'U']
      },
      {
        title: 'How It Works',
        url: '/dashboard/cms/how-it-works',
        shortcut: ['H', 'W']
      },
      {
        title: 'Contact Us',
        url: '/dashboard/cms/contact-us',
        shortcut: ['C', 'U']
      }
    ]
  }
  // {
  //   title: 'React Query',
  //   url: '#',
  //   icon: 'logo',
  //   isActive: true,
  //   items: [
  //     {
  //       title: 'Posts',
  //       url: '/posts',
  //       icon: 'post',
  //       shortcut: ['p', 'o']
  //     },
  //     {
  //       title: 'Profile',
  //       url: '/profile',
  //       icon: 'userPen',
  //       shortcut: ['p', 'r']
  //     },
  //     {
  //       title: 'Todos',
  //       url: '/todos',
  //       icon: 'check',
  //       shortcut: ['t', 'd']
  //     }
  //   ]
  // },
  // {
  //   title: 'Account',
  //   url: '#', // Placeholder as there is no direct link for the parent
  //   icon: 'billing',
  //   isActive: false,
  //   items: [
  //     {
  //       title: 'Profile',
  //       url: '/dashboard/profile',
  //       icon: 'userPen',
  //       shortcut: ['m', 'm']
  //     },
  //     {
  //       title: 'Login',
  //       shortcut: ['l', 'l'],
  //       url: '/',
  //       icon: 'login'
  //     }
  //   ]
  // },
  // {
  //   title: 'Kanban',
  //   url: '/dashboard/kanban',
  //   icon: 'kanban',
  //   shortcut: ['k', 'k'],
  //   isActive: false,
  //   items: [] // No child items
  // }
];

export interface SaleUser {
  id: number;
  name: string;
  email: string;
  amount: string;
  image: string;
  initials: string;
}

export const recentSalesData: SaleUser[] = [
  {
    id: 1,
    name: 'Olivia Martin',
    email: '<EMAIL>',
    amount: '+$1,999.00',
    image: 'https://api.slingacademy.com/public/sample-users/1.png',
    initials: 'OM'
  },
  {
    id: 2,
    name: 'Jackson Lee',
    email: '<EMAIL>',
    amount: '+$39.00',
    image: 'https://api.slingacademy.com/public/sample-users/2.png',
    initials: 'JL'
  },
  {
    id: 3,
    name: 'Isabella Nguyen',
    email: '<EMAIL>',
    amount: '+$299.00',
    image: 'https://api.slingacademy.com/public/sample-users/3.png',
    initials: 'IN'
  },
  {
    id: 4,
    name: 'William Kim',
    email: '<EMAIL>',
    amount: '+$99.00',
    image: 'https://api.slingacademy.com/public/sample-users/4.png',
    initials: 'WK'
  },
  {
    id: 5,
    name: 'Sofia Davis',
    email: '<EMAIL>',
    amount: '+$39.00',
    image: 'https://api.slingacademy.com/public/sample-users/5.png',
    initials: 'SD'
  }
];
