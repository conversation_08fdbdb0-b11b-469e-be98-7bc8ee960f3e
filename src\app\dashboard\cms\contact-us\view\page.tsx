'use client';

import { useRouter } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Edit } from 'lucide-react';
import { useGetContactUsPage } from '@/hooks/useQuery';
import Image from 'next/image';
import { DEFAULT_IMAGE } from '@/constants/app.const';

export default function ContactUsViewPage() {
  const router = useRouter();

  // Fetch Contact Us page data
  const { data: contactUsData, isLoading: contactUsLoading } = useGetContactUsPage();

  const handleBack = () => {
    router.push('/dashboard/cms/contact-us');
  };

  const handleEdit = () => {
    router.push('/dashboard/cms/contact-us/edit');
  };

  if (contactUsLoading) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col space-y-6'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-10 w-10' />
            <div>
              <Skeleton className='h-8 w-48' />
              <Skeleton className='h-4 w-64 mt-2' />
            </div>
          </div>
          <Card>
            <CardHeader>
              <Skeleton className='h-6 w-32' />
            </CardHeader>
            <CardContent>
              <Skeleton className='h-20 w-full' />
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    );
  }

  const pageData = contactUsData?.data?.page;

  if (!pageData) {
    return (
      <PageContainer>
        <div className='flex flex-1 flex-col items-center justify-center space-y-4'>
          <h2 className='text-2xl font-bold'>No Data Available</h2>
          <p className='text-muted-foreground'>
            Contact Us page data could not be loaded.
          </p>
          <Button onClick={handleBack}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Go Back
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className='flex flex-1 flex-col space-y-6'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Button variant='outline' size='icon' onClick={handleBack}>
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <div>
              <h1 className='text-3xl font-bold'>Contact Us Page - View</h1>
              <p className='text-muted-foreground'>
                View the Contact Us page content
              </p>
            </div>
          </div>
          <Button onClick={handleEdit}>
            <Edit className='mr-2 h-4 w-4' />
            Edit Page
          </Button>
        </div>

        {/* Main Section */}
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-xl'>Contact Us Section</CardTitle>
              <Badge variant='primary'>main-section</Badge>
            </div>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div>
              <h3 className='font-semibold text-lg mb-2'>{pageData.heading}</h3>
              <p className='text-muted-foreground leading-relaxed mb-4'>
                {pageData.description}
              </p>
              {pageData.image && (
                <div className='relative w-full h-64 rounded-lg overflow-hidden'>
                  <Image
                    src={pageData.image || DEFAULT_IMAGE}
                    alt='Contact Us'
                    fill
                    className='object-cover'
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle className='text-lg'>Page Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <span className='text-sm font-medium text-muted-foreground'>Page ID:</span>
                <p className='text-sm font-mono bg-muted px-2 py-1 rounded'>
                  {pageData._id}
                </p>
              </div>
              <div>
                <span className='text-sm font-medium text-muted-foreground'>Version:</span>
                <p className='text-sm'>
                  v{pageData.__v}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
